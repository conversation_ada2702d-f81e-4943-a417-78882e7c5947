import { Building2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>3 } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { BRANDS } from "@/constants/brands";

const Index = () => {
  const totalPendingReplies = BRANDS.reduce((sum, brand) => sum + brand.pendingReplies, 0);
  const activeBrands = BRANDS.filter(brand => brand.status === 'active').length;

  return (
    <div className="flex-1 bg-gradient-subtle min-h-screen">
      <div className="container mx-auto px-6 py-8 space-y-8">
        {/* Welcome Section */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-foreground">Brand Response AI</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Manage responses across all your brands with AI-powered assistance
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-3xl mx-auto">
          <Card className="text-center">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Active Brands
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{activeBrands}</div>
              <p className="text-xs text-muted-foreground">of {BRANDS.length} total</p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Pending Replies
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{totalPendingReplies}</div>
              <p className="text-xs text-muted-foreground">across all brands</p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                AI Assistance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-accent">Active</div>
              <p className="text-xs text-muted-foreground">ready to help</p>
            </CardContent>
          </Card>
        </div>

        {/* Brand Management Section */}
        <div className="space-y-6">
          <div className="flex items-center justify-between max-w-6xl mx-auto">
            <div>
              <h2 className="text-2xl font-bold text-foreground">Brand Management</h2>
              <p className="text-muted-foreground">Monitor and manage your brand responses</p>
            </div>
            <Button variant="outline" size="sm" className="hidden md:flex">
              <Building2 className="w-4 h-4 mr-2" />
              Add Brand
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {BRANDS.map((brand) => (
              <Card key={brand.id} className="group relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border-l-4 border-l-primary">
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary/10 to-transparent rounded-bl-full" />

                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-ai rounded-lg flex items-center justify-center">
                        <Building2 className="w-5 h-5 text-ai-foreground" />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold group-hover:text-primary transition-colors">
                          {brand.name}
                        </CardTitle>
                        <p className="text-sm text-muted-foreground">Brand Account</p>
                      </div>
                    </div>
                    <Badge
                      variant={brand.status === 'active' ? 'default' : 'secondary'}
                      className={brand.status === 'active' ? 'bg-accent text-accent-foreground' : ''}
                    >
                      {brand.status}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent className="space-y-6">
                  {/* Metrics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-muted/50 rounded-lg">
                      <div className={`text-2xl font-bold ${brand.pendingReplies > 10 ? 'text-destructive' : brand.pendingReplies > 5 ? 'text-warning' : 'text-accent'}`}>
                        {brand.pendingReplies}
                      </div>
                      <p className="text-xs text-muted-foreground">Pending</p>
                    </div>
                    <div className="text-center p-3 bg-muted/50 rounded-lg">
                      <div className="text-2xl font-bold text-primary">87%</div>
                      <p className="text-xs text-muted-foreground">Response Rate</p>
                    </div>
                  </div>

                  {/* Priority Indicator */}
                  {brand.pendingReplies > 0 && (
                    <div className="flex items-center gap-2 p-2 bg-primary-light/50 rounded-lg border border-primary/20">
                      <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                      <span className="text-sm font-medium text-primary">
                        {brand.pendingReplies > 10 ? 'High Priority' : brand.pendingReplies > 5 ? 'Medium Priority' : 'Low Priority'}
                      </span>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="grid grid-cols-2 gap-3">
                    <Button asChild variant="outline" className="group/btn">
                      <Link to={`/brand/${brand.id}/analytics`}>
                        <BarChart3 className="w-4 h-4 mr-2 group-hover/btn:scale-110 transition-transform" />
                        Analytics
                      </Link>
                    </Button>
                    <Button
                      asChild
                      variant={brand.pendingReplies > 0 ? "default" : "outline"}
                      className="group/btn relative"
                    >
                      <Link to={`/brand/${brand.id}/messages`}>
                        <MessageSquare className="w-4 h-4 mr-2 group-hover/btn:scale-110 transition-transform" />
                        Messages
                        {brand.pendingReplies > 0 && (
                          <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs bg-destructive text-destructive-foreground flex items-center justify-center">
                            {brand.pendingReplies > 99 ? '99+' : brand.pendingReplies}
                          </Badge>
                        )}
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;